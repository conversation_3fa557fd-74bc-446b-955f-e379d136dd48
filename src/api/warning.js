import request from '@/utils/request'

// 预警设置

export function warningConfig(data) {
    return request({
        url: '/mesapp-service/warning/config/info',
        method: 'post',
        data: data
    })
}

// 预警设置-默认详情
export function warningDefault(data) {
    return request({
        url: '/mesapp-service/warning/config/default',
        method: 'post',
        data: data
    })
}
// 预警设置-保存
export function warningEdit(data) {
    return request({
        url: '/mesapp-service/warning/config/edit',
        method: 'post',
        data: data
    })
}

// 预警设置-恢复默认
export function warningSetDefault(data) {
    return request({
        url: '/mesapp-service/warning/config/setDefault',
        method: 'post',
        data: data
    })
}

// 预警设置-接收人新增
export function warningReceiverAdd(data) {
    return request({
        url: '/mesapp-service/warning/config/receiver/add',
        method: 'post',
        data: data
    })
}

// 预警设置-接收人删除
export function warningReceiverRemove(data) {
    return request({
        url: '/mesapp-service/warning/config/receiver/remove',
        method: 'post',
        data: data
    })
}

// 预警设置-接收人列表
export function warningReceiverList(data) {
    return request({
        url: '/mesapp-service/warning/config/receiver/list',
        method: 'post',
        data: data
    })
}

// 企业员工列表
export function userList(data) {
    return request({
        url: 'admin-service/system/user/list',
        method: 'get',
        params: data
    })
}


// 预警记录列表
export function warningRecordPage(data) {
    return request({
        url: '/mesapp-service/warning/record/page',
         method: 'post',
        data: data
    })
}

// 预警处理
export function updateWarningStatus(data) {
    return request({
        url: '/mesapp-service/record/updateWarningStatus',
        method: 'post',
        data: data
    })
}

// 预警忽略
export function updateIgnoreStatus(data) {
    return request({
        url: '/mesapp-service/warning/record/updateIgnoreStatus',
        method: 'post',
        data: data
    })
}

// 预警记录-低库存导出
export function warningWarehouseExport(data) {
    return request({
        url: '/mesapp-service/warning/record/warehouse/export',
        method: 'post',
        data: data,
        responseType: 'blob',
    })
}

// 呆滞料预警
export function dormantInventory(data) {
    return request({
        url: '/mesapp-service/warning/record/dormantInventory/page',
        method: 'post',
        data: data
    })
}

// 保质期预警
export function expirationPage(data) {
    return request({
        url: '/mesapp-service/warning/record/expiration/page',
        method: 'post',
        data: data
    })
}